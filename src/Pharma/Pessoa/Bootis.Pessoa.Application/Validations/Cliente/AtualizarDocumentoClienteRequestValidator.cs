using Bootis.Pessoa.Application.Requests.Cliente.Atualizar;
using Bootis.Shared;
using FluentValidation;
using Microsoft.Extensions.Localization;

namespace Bootis.Pessoa.Application.Validations.Cliente;

public class AtualizarDocumentoClienteRequestValidator : AbstractValidator<AtualizarDocumentoRequest>
{
    public AtualizarDocumentoClienteRequestValidator(IStringLocalizer localizer)
    {
        RuleFor(a => a.ClienteDocumentoId)
            .NotNull()
            .WithMessage(f => localizer.GetMessage_Validation_CampoRequerido(nameof(f.ClienteDocumentoId)));

        RuleFor(c => c.TipoDocumentoId)
            .NotEmpty()
            .NotNull()
            .WithMessage(f => localizer.GetMessage_Validation_CampoRequerido(nameof(f.TipoDocumentoId)));

        RuleFor(c => c.Identificacao)
            .NotEmpty()
            .NotNull()
            .WithMessage(f => localizer.GetMessage_Validation_CampoRequerido(nameof(f.Identificacao)));

        RuleFor(c => c.Observacao)
            .MaximumLength(1000)
            .WithMessage(f => localizer.GetMessage_Validation_TamanhoMaximo(f.Observacao, 1000));
    }
}