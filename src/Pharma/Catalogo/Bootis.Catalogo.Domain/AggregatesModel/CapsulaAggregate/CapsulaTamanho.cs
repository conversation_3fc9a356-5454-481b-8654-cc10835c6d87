using System.ComponentModel.DataAnnotations.Schema;
using Bootis.Shared.Domain.SeedWork;

namespace Bootis.Catalogo.Domain.AggregatesModel.CapsulaAggregate;

[Table("capsulas_tamanho")]
public class CapsulaTamanho : Entity, IAggregateRoot
{
    public CapsulaTamanho()
    {
    }

    public CapsulaTamanho(string numeroCapsula,
        decimal volumeMl) : this()
    {
        NumeroCapsula = numeroCapsula;
        VolumeMl = volumeMl;
    }

    public string NumeroCapsula { get; private set; }
    public decimal VolumeMl { get; private set; }
}