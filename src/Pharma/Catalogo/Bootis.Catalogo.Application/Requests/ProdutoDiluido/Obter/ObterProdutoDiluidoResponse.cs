using Bootis.Shared.Common.UnidadeMedida.Enums;

namespace Bootis.Catalogo.Application.Requests.ProdutoDiluido.Obter;

public class ObterProdutoDiluidoResponse
{
    public Guid ProdutoId { get; set; }
    public string ProdutoDescricao { get; set; }
    public Guid FormaFarmaceuticaId { get; set; }
    public string FormaFarmaceuticaDescricao { get; set; }
    public decimal DosagemMinima { get; set; }
    public decimal DosagemMaxima { get; set; }
    public UnidadeMedidaAbreviacao UnidadeMedidaId { get; set; }
    public string UnidadeMedidaAbreviacao { get; set; }
    public decimal Diluicao { get; set; }
    public bool SeTodasFormasFarmaceuticas { get; set; }
    public bool SeQualquerDosagem { get; set; }
}