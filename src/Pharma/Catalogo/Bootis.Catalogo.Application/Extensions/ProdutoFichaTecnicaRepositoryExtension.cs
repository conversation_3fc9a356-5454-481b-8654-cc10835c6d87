using Bootis.Catalogo.Domain.AggregatesModel.ProdutoFichaTecnicaAggregate;
using Bootis.Catalogo.Resources;
using Bootis.Shared.Common;
using System.ComponentModel.DataAnnotations;

namespace Bootis.Catalogo.Application.Extensions;

public static class ProdutoFichaTecnicaRepositoryExtension
{
    public static async Task<ProdutoFichaTecnica> ObterProdutoFichaTecnicaAsync(this IProdutoFichaTecnicaRepository repository, Guid id)
    {
        var produtoFichaTecnica = await repository.ObterPorIdComIncludesAsync(id);

        if (produtoFichaTecnica is null)
        {
            throw new ValidationException(
                Localizer.Instance.GetMessage_ProdutoFichaTecnica_IdNaoEncontrado(id));
        }

        return produtoFichaTecnica;
    }

    public static async Task<ProdutoFichaTecnica> ObterProdutoFichaTecnicaPorProdutoIdAsync(this IProdutoFichaTecnicaRepository repository, Guid produtoId)
    {
        var produtoFichaTecnica = await repository.ObterPorProdutoIdComIncludesAsync(produtoId);

        if (produtoFichaTecnica is null)
        {
            throw new ValidationException(
                Localizer.Instance.GetMessage_ProdutoFichaTecnica_ProdutoNaoContemFichaTecnica(produtoId));
        }

        return produtoFichaTecnica;
    }
}