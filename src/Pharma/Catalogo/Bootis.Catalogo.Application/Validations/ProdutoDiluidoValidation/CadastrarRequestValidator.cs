using Bootis.Catalogo.Application.Requests.ProdutoDiluido.Cadastrar;
using Bootis.Catalogo.Resources;
using Bootis.Shared;
using Bootis.Shared.Common;
using FluentValidation;
using Microsoft.Extensions.Localization;

namespace Bootis.Catalogo.Application.Validations.ProdutoDiluidoValidation;

public class CadastrarRequestValidator : AbstractValidator<CadastrarRequest>
{
    public CadastrarRequestValidator(IStringLocalizer localizer)
    {
        RuleFor(c => c.ProdutoId)
            .NotEmpty()
            .WithMessage(l => localizer.GetMessage_Validation_CampoRequerido(nameof(l.ProdutoId)));

        RuleFor(c => c.FormaFarmaceuticaId)
            .NotEmpty()
            .When(c => !c.SeTodasFormasFarmaceuticas)
            .WithMessage(l => localizer.GetMessage_Validation_CampoRequerido(nameof(l.FormaFarmaceuticaId)));

        RuleFor(c => c.DosagemMinima)
            .NotEmpty()
            .When(c => !c.SeQualquerDosagem)
            .WithMessage(localizer.GetMessage_ProdutoDiluido_DosagemObrigatoria());

        RuleFor(c => c.DosagemMaxima)
            .NotEmpty()
            .When(c => !c.SeQualquerDosagem)
            .WithMessage(localizer.GetMessage_ProdutoDiluido_DosagemObrigatoria());

        RuleFor(c => c.UnidadeMedidaId)
            .IsInEnum();

        RuleFor(c => c.Diluicao)
            .NotEmpty()
            .WithMessage(l => localizer.GetMessage_Validation_CampoRequerido(nameof(l.Diluicao)));

        When(c => !c.SeQualquerDosagem, () =>
        {
            RuleFor(c => c.DosagemMaxima)
                .GreaterThanOrEqualTo(c => c.DosagemMinima)
                .WithMessage(Localizer.Instance.GetMessage_Produto_DosagemInvalida());
        });
    }
}