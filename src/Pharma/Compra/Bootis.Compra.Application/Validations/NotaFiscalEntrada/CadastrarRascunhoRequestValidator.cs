using Bootis.Compra.Application.Requests.NotaFiscalEntrada.Cadastrar;
using Bootis.Compra.Domain.Dtos.NotaFiscalEntrada;
using Bootis.Shared;
using FluentValidation;
using Microsoft.Extensions.Localization;

namespace Bootis.Compra.Application.Validations.NotaFiscalEntrada;

public class CadastrarRascunhoRequestValidator : AbstractValidator<CadastrarRascunhoRequest>
{
    public CadastrarRascunhoRequestValidator(IStringLocalizer localizer)
    {
        RuleFor(c => c.Numero)
            .NotEmpty()
            .WithMessage(c => localizer.GetMessage_Validation_CampoRequerido(nameof(c.Numero)));

        RuleFor(c => c.Serie)
            .NotEmpty()
            .WithMessage(c => localizer.GetMessage_Validation_CampoRequerido(nameof(c.Serie)));

        RuleFor(c => c.DataEmissao)
            .NotEmpty()
            .LessThanOrEqualTo(c => c.DataEntrega)
            .WithMessage(c => localizer.GetMessage_Validation_CampoRequerido(nameof(c.DataEmissao)));

        RuleFor(c => c.DataEntrega)
            .NotEmpty()
            .GreaterThanOrEqualTo(c => c.DataEmissao)
            .WithMessage(c => localizer.GetMessage_Validation_CampoRequerido(nameof(c.DataEntrega)));

        RuleFor(c => c.NaturezaOperacaoId)
            .NotEmpty()
            .WithMessage(c => localizer.GetMessage_Validation_CampoRequerido(nameof(c.NaturezaOperacaoId)));

        RuleFor(c => c.FornecedorId)
            .NotEmpty()
            .WithMessage(c => localizer.GetMessage_Validation_CampoRequerido(nameof(c.NaturezaOperacaoId)));

        RuleFor(c => c.TipoFreteId)
            .NotEmpty()
            .WithMessage(c => localizer.GetMessage_Validation_CampoRequerido(nameof(c.TipoFreteId)));

        RuleForEach(c => c.Itens)
            .SetValidator(new CadastrarNotaFiscalItemRascunhoRequestValidator(localizer));
    }
}

public class CadastrarNotaFiscalItemRascunhoRequestValidator : AbstractValidator<NotaFiscalEntradaItemRascunhoDto>
{
    public CadastrarNotaFiscalItemRascunhoRequestValidator(IStringLocalizer localizer)
    {
        RuleFor(c => c.ProdutoId)
            .NotEmpty()
            .WithMessage(c => localizer.GetMessage_Validation_CampoRequerido(nameof(c.ProdutoId)));

        RuleFor(c => c.NcmId)
            .NotEmpty()
            .WithMessage(c => localizer.GetMessage_Validation_CampoRequerido(nameof(c.NcmId)));

        RuleFor(c => c.CfopId)
            .NotEmpty()
            .WithMessage(c => localizer.GetMessage_Validation_CampoRequerido(nameof(c.CfopId)));

        RuleFor(c => c.CstCsosnId)
            .NotEmpty()
            .WithMessage(c => localizer.GetMessage_Validation_CampoRequerido(nameof(c.CstCsosnId)));

        RuleFor(c => c.UnidadeMedidaId)
            .IsInEnum();

        RuleFor(c => c.QuantidadeComprada)
            .NotNull()
            .WithMessage(c => localizer.GetMessage_Validation_CampoRequerido(nameof(c.QuantidadeComprada)));

        RuleFor(c => c.ValorUnitario)
            .NotNull()
            .WithMessage(c => localizer.GetMessage_Validation_CampoRequerido(nameof(c.ValorUnitario)));

        RuleFor(c => c.BaseCalculoIcms)
            .NotNull()
            .WithMessage(c => localizer.GetMessage_Validation_CampoRequerido(nameof(c.BaseCalculoIcms)));

        RuleFor(c => c.ValorIcms)
            .NotNull()
            .WithMessage(c => localizer.GetMessage_Validation_CampoRequerido(nameof(c.ValorIcms)));

        RuleFor(c => c.AliquotaIpi)
            .NotNull()
            .WithMessage(c => localizer.GetMessage_Validation_CampoRequerido(nameof(c.AliquotaIpi)));

        RuleFor(c => c.ValorIpi)
            .NotNull()
            .WithMessage(c => localizer.GetMessage_Validation_CampoRequerido(nameof(c.AliquotaIpi)));
    }
}