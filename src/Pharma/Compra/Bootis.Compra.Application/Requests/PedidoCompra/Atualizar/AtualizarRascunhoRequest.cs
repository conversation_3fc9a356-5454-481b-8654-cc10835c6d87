using Bootis.Compra.Domain.Dtos.PedidoCompra;
using MediatR;

namespace Bootis.Compra.Application.Requests.PedidoCompra.Atualizar;

public class AtualizarRascunhoRequest : IRequest, IPedidoCompraRascunhoDto
{
    public Guid Id { get; set; }
    public IEnumerable<AtualizarItemRascunhoRequest> PedidoCompraItens { get; set; }
    public Guid? FornecedorId { get; set; }
    public DateOnly? PrevisaoEntrega { get; set; }
    public string Observacao { get; set; }
    public decimal? Frete { get; set; }
    public decimal? ValorAdicional { get; set; }
    public Domain.Enumerations.TipoDesconto? TipoDesconto { get; set; }
    public decimal? ValorDesconto { get; set; }
}

public class AtualizarItemRascunhoRequest : PedidoCompraItemRascunhoDto
{
    public Guid? PedidoCompraItemId { get; set; }
}