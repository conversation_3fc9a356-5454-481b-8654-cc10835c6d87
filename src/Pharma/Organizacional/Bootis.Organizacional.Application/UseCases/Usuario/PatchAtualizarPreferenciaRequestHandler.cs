using Bootis.Organizacional.Application.Requests.Usuario.Atualizar;
using Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate;
using MediatR;

namespace Bootis.Organizacional.Application.UseCases.Usuario;

public class PatchAtualizarPreferenciaRequestHandler(
    IMediator mediator,
    IUsuarioRepository usuarioRepository)
    : IRequestHandler<PatchAtualizarPreferenciaRequest>
{
    public async Task Handle(PatchAtualizarPreferenciaRequest request, CancellationToken cancellationToken)
    {
        var usuario = await usuarioRepository.GetByIdAsync(request.UserId);

        if (usuario == null)
            return;

        var preferencias = usuario.Preferencias ?? new PreferenciasUsuario();

        var command = new AtualizarPreferenciaRequest
        {
            TemaUsuario = preferencias.TemaUsuario,
            TextoNegrito = preferencias.TextoNegrito,
            TextoAmpliado = preferencias.TextoAmpliado,
            ContrasteAumentado = preferencias.ContrasteAumentado,
            TimeZone = preferencias.TimeZone,
            Idioma = preferencias.Idioma,
            PadraoData = preferencias.PadraoData,
            PadraoHora = preferencias.PadraoHora
        };

        request.PathcDocument.ApplyTo(command);

        await mediator.Send(command, cancellationToken);
    }
}