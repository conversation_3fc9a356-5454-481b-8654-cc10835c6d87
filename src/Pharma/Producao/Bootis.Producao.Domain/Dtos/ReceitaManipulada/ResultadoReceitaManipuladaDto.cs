using Bootis.Catalogo.Domain.Enumerations;
using Bootis.Pessoa.Domain.Enumerations;
using Bootis.Producao.Domain.Enumerations;

namespace Bootis.Producao.Domain.Dtos.ReceitaManipulada;

public class ReceitaManipuladaResultadoDto
{
    public decimal QuantidadeReceita { get; set; }
    public int QuantidadeRepetir { get; set; }
    public decimal? QuantidadeDose { get; set; }
    
    public bool UsoContinuo { get; set; }
    public TipoCalculoUsoContinuo? TipoUsoContinuo { get; set; }
    public decimal? QuantidadeReSell { get; set; }
    public PeriodosPosologia? PeriodicidadeReSell { get; set; }
    public int? DuracaoPrevistaReSellDias { get; set; }
    public DateTime? PrevisaoTerminoReSell { get; set; }

    public string FormaFarmaceuticaApresentacao { get; set; }
    public Guid FormaFarmaceuticaId { get; set; }

    public string PacienteDescricao { get; set; }
    public Guid PacienteId { get; set; }
    public TipoPessoa PacienteTipoPessoa { get; set; }
    public string PacienteCnpj { get; set; }
    public string PacienteCpf { get; set; }

    public string PrescritorDescricao { get; set; }
    public Guid? PrescritorId { get; set; }
    
    public string PrescritorSiglaRegistro { get; set; }
    public string PrescritorCodigoRegistro { get; set; }
    public string PrescritorUfRegistro { get; set; }

    public List<ReceitaManipuladaItemResultadoDto> Componentes { get; set; }
}

public class ReceitaManipuladaItemResultadoDto
{
    public Guid ProdutoId { get; set; }
    public Guid? ProdutoSinonimoId { get; set; }
    public string DescricaoProduto { get; set; }
    public decimal Quantidade { get; set; }
    public int UnidadeMedidaId { get; set; }
    public string UnidadeMedidaAbreviacao { get; set; }
    public TipoComponente TipoQuantificacao { get; set; }
    public int Ordem { get; set; }
    public Guid? FormulaPadraoId { get; set; }
}