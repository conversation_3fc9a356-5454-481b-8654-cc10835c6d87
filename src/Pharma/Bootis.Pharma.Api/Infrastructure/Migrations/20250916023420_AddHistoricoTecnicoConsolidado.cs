using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Bootis.Pharma.Api.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddHistoricoTecnicoConsolidado : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "fk_certificados_analise_especificacao_bibliografias_bibliograf",
                table: "certificados_analise_especificacao");

            migrationBuilder.DropIndex(
                name: "ix_certificados_analise_especificacao_bibliografia_id",
                table: "certificados_analise_especificacao");

            migrationBuilder.DropColumn(
                name: "bibliografia_id",
                table: "certificados_analise_especificacao");

            migrationBuilder.CreateTable(
                name: "receitas_manipuladas_historico_tecnico",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    receita_manipulada_id = table.Column<Guid>(type: "uuid", nullable: false),
                    usuario_id = table.Column<Guid>(type: "uuid", nullable: false),
                    tipo_registro = table.Column<int>(type: "integer", nullable: false),
                    descricao = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: false),
                    produtos_envolvidos = table.Column<string[]>(type: "text[]", nullable: true),
                    data_movimento = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    is_removed = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_receitas_manipuladas_historico_tecnico", x => x.id);
                    table.ForeignKey(
                        name: "fk_receitas_manipuladas_historico_tecnico_receitas_manipuladas",
                        column: x => x.receita_manipulada_id,
                        principalTable: "receitas_manipuladas",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "fk_receitas_manipuladas_historico_tecnico_usuarios_usuario_id",
                        column: x => x.usuario_id,
                        principalTable: "usuarios",
                        principalColumn: "id");
                });

            migrationBuilder.CreateIndex(
                name: "ix_receitas_manipuladas_historico_tecnico_receita_manipulada_id",
                table: "receitas_manipuladas_historico_tecnico",
                column: "receita_manipulada_id");

            migrationBuilder.CreateIndex(
                name: "ix_receitas_manipuladas_historico_tecnico_usuario_id",
                table: "receitas_manipuladas_historico_tecnico",
                column: "usuario_id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "receitas_manipuladas_historico_tecnico");

            migrationBuilder.AddColumn<Guid>(
                name: "bibliografia_id",
                table: "certificados_analise_especificacao",
                type: "uuid",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "ix_certificados_analise_especificacao_bibliografia_id",
                table: "certificados_analise_especificacao",
                column: "bibliografia_id");

            migrationBuilder.AddForeignKey(
                name: "fk_certificados_analise_especificacao_bibliografias_bibliograf",
                table: "certificados_analise_especificacao",
                column: "bibliografia_id",
                principalTable: "bibliografias",
                principalColumn: "id");
        }
    }
}
