using Bootis.Shared.Common.Extensions;
using Bootis.Shared.Infrastructure;
using Microsoft.EntityFrameworkCore;

namespace Bootis.Venda.Infrastructure.Seeds;

public class StatusAtendimentoSeed : ISeed
{
    public int Order => 2;

    public void Seed(DbContext dbContext)
    {
        dbContext.Database.ExecuteSqlRaw($"""
                                          INSERT INTO status_atendimento 
                                              (id, descricao, ativo, ordem, group_tenant_id, usuario_id, tenant_id)
                                          VALUES
                                              ('{1.ToGuid()}', 'Aguardando Atendimento', true, 1, '11111111-1111-1111-1111-111111111111', '11111111-1111-1111-1111-111111111111', '11111111-1111-1111-1111-111111111111'),
                                              ('{2.ToGuid()}', 'Em Atendimento', true, 2, '11111111-1111-1111-1111-111111111111', '11111111-1111-1111-1111-111111111111', '11111111-1111-1111-1111-111111111111'),
                                              ('{3.ToGuid()}', 'Orçamento Enviado', true, 3, '11111111-1111-1111-1111-111111111111', '11111111-1111-1111-1111-111111111111', '11111111-1111-1111-1111-111111111111'),
                                              ('{4.ToGuid()}', 'Atendimento Concluído', true, 4, '11111111-1111-1111-1111-111111111111', '11111111-1111-1111-1111-111111111111', '11111111-1111-1111-1111-111111111111'),
                                              ('{5.ToGuid()}', 'Aguardando Receita', true, 5, '11111111-1111-1111-1111-111111111111', '11111111-1111-1111-1111-111111111111', '11111111-1111-1111-1111-111111111111'),
                                              ('{6.ToGuid()}', 'Finalizado', true, 6, '11111111-1111-1111-1111-111111111111', '11111111-1111-1111-1111-111111111111', '11111111-1111-1111-1111-111111111111');
                                          """);
    }
}