using Bootis.Estoque.Domain.AggregatesModel.InventarioAggregate;
using Bootis.Shared.Infrastructure.EntityConfigurations;
using Bootis.Shared.Infrastructure.Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bootis.Estoque.Infrastructure.EntityConfigurations;

public class InventarioHistoricoEntityTypeConfiguration : BaseEntityTypeConfiguration<InventarioHistorico>
{
    public override void Configure(EntityTypeBuilder<InventarioHistorico> builder)
    {
        builder.ToTable("inventario_historicos");

        builder
            .HasOne(c => c.InventarioLancamento)
            .WithMany(c => c.InventarioHistorico)
            .HasForeignKey(c => c.InventarioLancamentoId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Cascade);

        builder
            .HasOne(c => c.<PERSON>)
            .WithMany()
            .HasForeignKey(c => c.UsuarioId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .Property(c => c.Data)
            .DataHora()
            .IsRequired();

        builder
            .Property(c => c.Status)
            .IsRequired();

        base.Configure(builder);
    }
}