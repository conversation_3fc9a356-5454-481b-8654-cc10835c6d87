using Bootis.Estoque.Application.Extensions;
using Bootis.Estoque.Application.Requests.Lote.Remover;
using Bootis.Estoque.Domain.AggregatesModel.LoteAggregate;
using Bootis.Shared.Application.Interfaces;
using MediatR;

namespace Bootis.Estoque.Application.UseCases.Lote;

public class RemoverRequestHandler(
    IUnitOfWork unitOfWork,
    ILoteRepository loteRepository) : IRequestHandler<RemoverRequest>
{
    public async Task Handle(RemoverRequest request, CancellationToken cancellationToken)
    {
        var lotes = await loteRepository.ObterLotesAsync(request.LotesId);

        await loteRepository.VerificarDependenciasAsync(lotes);

        foreach (var loteId in request.LotesId)
        {
            var lote = lotes.Single(l => l.Id == loteId);

            lote.Remove();
            loteRepository.Remove(lote);
        }

        await unitOfWork.SaveChangesAsync(cancellationToken).ConfigureAwait(false);
    }
}