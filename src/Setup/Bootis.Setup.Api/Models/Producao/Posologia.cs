using Bootis.Setup.Api.Attributes;
using UUIDNext;

namespace Bootis.Setup.Api.Models.Producao;

[TableName("posologias")]
public record class Posologia
{
    public Guid Id { get; set; } = Uuid.NewSequential();
    public string Descricao { get; set; }
    [IgnoreColumn(Destination = true)] public string FormaFarmaceuticaDescricao { get; set; }
    [IgnoreColumn(Source = true)] public Guid FormaFarmaceuticaId { get; set; }
    public decimal QuantidadeDosePorPeriodo { get; set; }
    [IgnoreColumn(Destination = true)] public string UnidadeMedidaDescricao { get; set; }
    [IgnoreColumn(Source = true)] public int UnidadeMedidaId { get; set; }
    [IgnoreColumn(Destination = true)] public string PeriodoDescricao { get; set; }
    [IgnoreColumn(Source = true)] public int Periodo { get; set; }
    public bool Ativo { get; set; } = true;
    public Guid GroupTenantId { get; set; }
    public Guid TenantId { get; set; }
}