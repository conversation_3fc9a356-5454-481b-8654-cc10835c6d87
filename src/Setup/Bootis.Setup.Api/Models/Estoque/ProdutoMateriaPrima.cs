using Bootis.Setup.Api.Attributes;

namespace Bootis.Setup.Api.Models.Estoque;

[TableName("produtos_materia_prima")]
public record class ProdutoMateriaPrima
{
    [IgnoreColumn(Destination = true)] public string ProdutoDescricao { get; set; }
    [IgnoreColumn(Source = true)] public Guid ProdutoId { get; set; }
    [IgnoreColumn(Destination = true)] public int? NumeroDcb { get; set; }
    [IgnoreColumn(Source = true)] public Guid? DcbId { get; set; }
    [IgnoreColumn(Destination = true)] public string NumeroCas { get; set; }
    [IgnoreColumn(Source = true)] public Guid? CasId { get; set; }
    [IgnoreColumn(Destination = true)] public string UnidadePrescricaoDescricao { get; set; }
    [IgnoreColumn(Source = true)] public int UnidadePrescricaoId { get; set; }
    [IgnoreColumn(Destination = true)] public string TipoComponenteDescricao { get; set; }
    [IgnoreColumn(Source = true)] public int TipoComponenteId { get; set; }
    public bool? SomenteLaboratorio { get; set; }
    public bool? IsPellets { get; set; }
    public bool? IsExcipiente { get; set; }
    public bool? IsQsp { get; set; }
    public bool? SomenteDiluido { get; set; }
    public int? DiasValidade { get; set; }
    public bool? ExigeCapsulaGastroresistente { get; set; }
    public bool? IsMonodroga { get; set; }
    public decimal? ToleranciaPesagemUp { get; set; }
    public decimal? ToleranciaPesagemDown { get; set; }
    public decimal PesoMolecularSal { get; set; }
    public decimal PesoMolecularBase { get; set; }
    public decimal FatorEquivalencia { get; set; }
    public decimal? Valencia { get; set; }
    public string ObservacaoRotuloArmazenagem { get; set; } = "";
}