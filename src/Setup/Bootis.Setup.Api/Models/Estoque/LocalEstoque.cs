using Bootis.Setup.Api.Attributes;
using UUIDNext;

namespace Bootis.Setup.Api.Models.Estoque;

[TableName("locais_estoque")]
public record class LocalEstoque
{
    public Guid Id { get; set; } = Uuid.NewSequential();
    public Guid EmpresaId { get; set; }
    public string Descricao { get; set; }
    public int TipoEstoque { get; set; }
    public int SequenciaGroupTenant { get; set; }
    public bool Ativo { get; set; }
    public Guid TenantId { get; set; }
    public Guid GroupTenantId { get; set; }
}