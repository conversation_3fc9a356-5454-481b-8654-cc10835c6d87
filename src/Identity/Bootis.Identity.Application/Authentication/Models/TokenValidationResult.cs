using Bootis.Shared.Common.ValueObjects.Auth;

namespace Bootis.Identity.Application.Authentication.Models;

public class TokenValidationResult(
    Guid userId,
    Guid sessionId,
    DateTimeOffset? expiresAt = null,
    Dictionary<string, object> additionalClaims = null)
{
    public Guid UserId { get; } = userId;
    public Guid SessionId { get; } = sessionId;
    public DateTimeOffset ValidatedAt { get; } = DateTimeOffset.UtcNow;
    public DateTimeOffset? ExpiresAt { get; } = expiresAt;
    public Dictionary<string, object> AdditionalClaims { get; } = additionalClaims ?? new Dictionary<string, object>();

    public bool IsValid => !ExpiresAt.HasValue || ExpiresAt.Value > DateTimeOffset.UtcNow;

    public TimeSpan? TimeToExpiration => ExpiresAt.HasValue ? ExpiresAt.Value - DateTimeOffset.UtcNow : null;
}

public class TokenValidationError(
    AuthenticationError errorCode,
    bool isActive,
    string errorMessage = null,
    Dictionary<string, object> additionalData = null)
{
    public AuthenticationError ErrorCode { get; } = errorCode;
    public bool IsActive { get; } = isActive;
    public string ErrorMessage { get; } = errorMessage ?? GetDefaultErrorMessage(errorCode);
    public DateTimeOffset ErrorTimestamp { get; } = DateTimeOffset.UtcNow;
    public Dictionary<string, object> AdditionalData { get; } = additionalData ?? new Dictionary<string, object>();

    private static string GetDefaultErrorMessage(AuthenticationError errorCode)
    {
        return errorCode switch
        {
            AuthenticationError.InvalidToken => "The token is invalid or malformed",
            AuthenticationError.ExpiredToken => "The token has expired",
            AuthenticationError.RevokedToken => "The token has been revoked",
            AuthenticationError.MalformedToken => "The token format is invalid",
            AuthenticationError.UserNotFound => "User not found",
            AuthenticationError.UserInactive => "User account is inactive",
            AuthenticationError.SessionExpired => "Session has expired",
            AuthenticationError.SessionRevoked => "Session has been revoked",
            _ => "Token validation failed"
        };
    }
}