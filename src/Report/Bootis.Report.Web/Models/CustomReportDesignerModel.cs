using DevExpress.XtraReports.Web.ReportDesigner;

namespace Bootis.Report.Web.Models;

public class CustomReportDesignerModel
{
    public required ReportDesignerModel ReportDesignerModel { get; init; }
    public required string ReportType { get; init; }
    public required int Theme { get; init; } = 1;

    public required Guid TenantId { get; init; }
    public required Guid GroupTenantId { get; init; }
    
    public string Id { get; set; } = string.Empty;
}