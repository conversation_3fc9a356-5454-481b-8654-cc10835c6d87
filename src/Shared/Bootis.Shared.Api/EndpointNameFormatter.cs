using MassTransit;

namespace Bootis.Shared.Api;

internal class EndpointNameFormatter : DefaultEndpointNameFormatter
{
    private const char SEPARATOR = ':';

    private EndpointNameFormatter()
    {
    }

    public EndpointNameFormatter(string prefix)
        : base($"{prefix}{SEPARATOR}", false)
    {
    }

    public new static IEndpointNameFormatter Instance { get; } = new EndpointNameFormatter();
}