using Bootis.Shared.Common.UnidadeMedida.Enums;

namespace Bootis.Shared.Common.UnidadeMedida.UnidadeMedidaAggregate.Specializations;

public class UnidadeMedidaBILHAO : UnidadeMedida
{
    public override UnidadeMedidaAbreviacao Abreviacao => UnidadeMedidaAbreviacao.BILHAO;
    public override string Descricao => "Bilhão";
    public override bool Ativo => true;
    public override bool UnidadeAlternativa => false;
    public override bool UnidadeEstoque => false;
    public override bool UnidadePrescricao => true;
    public override bool UnidadePosologia => false;
    public override TipoUnidade TipoUnidade => TipoUnidade.Conversao;
}