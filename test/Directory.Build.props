<Project>

    <PropertyGroup>
        <IsTestProject>true</IsTestProject>
        <VSTestUseMSBuildOutput>false</VSTestUseMSBuildOutput>
        <VSTestTreatNoTestsAsError>false</VSTestTreatNoTestsAsError>
        <RunAnalyzersDuringBuild>false</RunAnalyzersDuringBuild>
        <RunCodeAnalysis>false</RunCodeAnalysis>
        <SkipAnalyzers>true</SkipAnalyzers>
        <GenerateDocumentationFile>false</GenerateDocumentationFile>
        <NoWarn>$(NoWarn);CS1591</NoWarn>
    </PropertyGroup>

    <PropertyGroup Condition="'$(Configuration)' == 'Release'">
        <Optimize>true</Optimize>
        <DebugType>portable</DebugType>
        <DebugSymbols>true</DebugSymbols>
    </PropertyGroup>

    <PropertyGroup>
        <CollectCoverage>true</CollectCoverage>
        <CoverletOutputFormat>cobertura</CoverletOutputFormat>
        <ExcludeByFile>**/Migrations/**/*</ExcludeByFile>
        <Exclude>[*.Tests]*,[*]*.Migrations.*</Exclude>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.14.1" />
        <PackageReference Include="coverlet.collector" Version="6.0.4">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="coverlet.msbuild" Version="6.0.4">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
    </ItemGroup>

</Project>