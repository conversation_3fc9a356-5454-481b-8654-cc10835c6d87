using Bootis.Pessoa.Application.Requests.Cliente.Remover;
using Bootis.Pessoa.Application.UseCases.Cliente;
using Bootis.Pessoa.Domain.AggregatesModel.ClienteAggregate;
using Bootis.Pharma.UnitTests.Common.Fakes;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.UnitTests;
using Moq;
using UUIDNext;

namespace Bootis.Pessoa.UnitTests.Application.ClienteContato;

public class RemoverContatoRequestHandlerTests : BaseTest
{
    private readonly Mock<IClienteRepository> _clienteContatoRepositoryMock;
    private readonly RemoverContatoRequestHandler _handler;
    private readonly Mock<IUnitOfWork> unitOfWork = new();


    public RemoverContatoRequestHandlerTests()
    {
        _clienteContatoRepositoryMock = new Mock<IClienteRepository>();

        _handler = new RemoverContatoRequestHandler(unitOfWork.Object, _clienteContatoRepositoryMock.Object);
    }

    [Fact]
    public async Task RemoverClienteContato_ExecutadoComSucesso()
    {
        //Arrange
        var cmd = new RemoverContatoRequest(Uuid.NewSequential());
        var model = ClienteContatoFake.CriarCommandValido();

        _clienteContatoRepositoryMock.Setup(l => l.ObterContatoPorContatoIdAsync(It.IsAny<Guid>()))
            .ReturnsAsync(model);

        _clienteContatoRepositoryMock.Setup(l => l.Remove(model))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .ReturnsAsync(1);

        //Action
        await _handler.Handle(cmd, default);

        //Assert
        _clienteContatoRepositoryMock
            .Verify(
                l => l.Remove(
                    It.Is<Pessoa.Domain.AggregatesModel.ClienteAggregate.ClienteContato>(m =>
                        m.Identificacao == model.Identificacao)),
                Times.Once);
    }

    [Fact]
    public async Task RemoveClienteContato_ExecutadoComErro()
    {
        //Arrange
        var cmd = new RemoverContatoRequest(Uuid.NewSequential());

        _clienteContatoRepositoryMock.Setup(l => l.ObterContatoPorContatoIdAsync(It.IsAny<Guid>()))
            .ReturnsAsync((Pessoa.Domain.AggregatesModel.ClienteAggregate.ClienteContato)null);

        _clienteContatoRepositoryMock.Setup(l =>
                l.Remove(It.IsAny<Pessoa.Domain.AggregatesModel.ClienteAggregate.ClienteContato>()))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .ReturnsAsync(1);

        //Action
        var exception = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(exception);
        unitOfWork
            .Verify(l => l.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Never);
    }
}
